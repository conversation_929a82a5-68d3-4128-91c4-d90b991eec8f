import 'package:flutter/material.dart';
import 'eye_widget.dart';
import 'mouth_widget.dart';
import 'face_widget.dart';

void main() {
  runApp(const MainApp());
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Ugly Avatar - Shape Generator',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const UglyAvatarDemo(),
    );
  }
}

class UglyAvatarDemo extends StatelessWidget {
  const UglyAvatarDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ugly Avatar - Shape Generator'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: const Center(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Ugly Avatar Shape Generator',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 10),
              Text(
                'Flutter port of the JavaScript ugly-avatar project',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16, color: Colors.grey),
              ),
              SizedBox(height: 40),

              // Eyes Section
              Text(
                '👀 Eyes',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 20),
              RandomEyesWidget(
                eyeSize: 100,
                spacing: 25,
                eyeColor: Colors.black,
                strokeWidth: 2.0,
                filled: false,
                eyeWidth: 50,
              ),
              SizedBox(height: 40),

              // Mouths Section
              Text(
                '👄 Mouths',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 20),
              AllMouthTypesWidget(
                size: 100,
                spacing: 15,
                mouthColor: Colors.red,
                strokeWidth: 2.0,
                filled: false,
                faceWidth: 80,
                faceHeight: 100,
              ),
              SizedBox(height: 40),

              // Individual Random Mouth
              Text(
                'Random Mouth Generator',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 20),
              RandomMouthWidget(
                size: 120,
                mouthColor: Colors.deepOrange,
                strokeWidth: 2.5,
                filled: false,
                faceWidth: 90,
                faceHeight: 110,
              ),
              SizedBox(height: 40),

              // Faces Section
              Text(
                '😊 Faces',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 20),
              MultipleFacesWidget(
                faceCount: 3,
                faceSize: 120,
                spacing: 15,
                faceColor: Colors.brown,
                strokeWidth: 2.0,
                filled: false,
                numPoints: 80,
              ),
              SizedBox(height: 40),

              // Individual Random Face
              Text(
                'Random Face Generator',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 20),
              RandomFaceWidget(
                size: 150,
                faceColor: Colors.deepPurple,
                strokeWidth: 2.5,
                filled: false,
                numPoints: 100,
              ),
              SizedBox(height: 40),

              // Face Comparison
              Text(
                'Face Comparison',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 20),
              FaceComparisonWidget(
                size: 120,
                spacing: 25,
                faceColor: Colors.orange,
                strokeWidth: 2.0,
                numPoints: 80,
              ),
              SizedBox(height: 40),

              // Filled versions
              Text(
                'Filled Versions',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Column(
                    children: [
                      Text('Eyes', style: TextStyle(fontWeight: FontWeight.w500)),
                      SizedBox(height: 10),
                      RandomEyesWidget(
                        eyeSize: 70,
                        spacing: 12,
                        eyeColor: Colors.blue,
                        strokeWidth: 1.0,
                        filled: true,
                        eyeWidth: 35,
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      Text('Mouth', style: TextStyle(fontWeight: FontWeight.w500)),
                      SizedBox(height: 10),
                      RandomMouthWidget(
                        size: 70,
                        mouthColor: Colors.pink,
                        strokeWidth: 1.0,
                        filled: true,
                        faceWidth: 50,
                        faceHeight: 70,
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      Text('Face', style: TextStyle(fontWeight: FontWeight.w500)),
                      SizedBox(height: 10),
                      RandomFaceWidget(
                        size: 70,
                        faceColor: Colors.amber,
                        strokeWidth: 1.0,
                        filled: true,
                        numPoints: 60,
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
