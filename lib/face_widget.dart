import 'package:flutter/material.dart';
import 'face_shape.dart';
import 'eye_shape.dart'; // For Point2D

/// Custom painter for drawing face shapes
class FacePainter extends CustomPainter {
  final FaceContour faceContour;
  final Color faceColor;
  final double strokeWidth;
  final bool filled;

  FacePainter({
    required this.faceContour,
    this.faceColor = Colors.brown,
    this.strokeWidth = 2.0,
    this.filled = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = faceColor
      ..strokeWidth = strokeWidth
      ..style = filled ? PaintingStyle.fill : PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    // Calculate center and scale
    final double centerX = size.width / 2;
    final double centerY = size.height / 2;
    final double scaleX = size.width / (faceContour.width * 2.5);
    final double scaleY = size.height / (faceContour.height * 2.5);
    final double scale = scaleX < scaleY ? scaleX : scaleY;

    // Create path for face
    final Path facePath = Path();
    if (faceContour.points.isNotEmpty) {
      final Point2D firstPoint = faceContour.points.first;
      facePath.moveTo(
        centerX + firstPoint.x * scale,
        centerY + firstPoint.y * scale,
      );

      for (int i = 1; i < faceContour.points.length; i++) {
        final Point2D point = faceContour.points[i];
        facePath.lineTo(
          centerX + point.x * scale,
          centerY + point.y * scale,
        );
      }

      // Close the path
      facePath.close();
    }

    // Draw the face shape
    canvas.drawPath(facePath, paint);

    // Optionally draw center point for debugging
    if (false) { // Set to true for debugging
      final Paint centerPaint = Paint()
        ..color = Colors.red
        ..style = PaintingStyle.fill;
      canvas.drawCircle(
        Offset(centerX, centerY),
        3,
        centerPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate != this;
  }
}

/// Widget for displaying a single face
class FaceWidget extends StatelessWidget {
  final FaceContour faceContour;
  final double size;
  final Color faceColor;
  final double strokeWidth;
  final bool filled;

  const FaceWidget({
    super.key,
    required this.faceContour,
    this.size = 200,
    this.faceColor = Colors.brown,
    this.strokeWidth = 2.0,
    this.filled = false,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CustomPaint(
        painter: FacePainter(
          faceContour: faceContour,
          faceColor: faceColor,
          strokeWidth: strokeWidth,
          filled: filled,
        ),
      ),
    );
  }
}

/// Widget that generates and displays random face shapes
class RandomFaceWidget extends StatefulWidget {
  final double size;
  final Color faceColor;
  final double strokeWidth;
  final bool filled;
  final int numPoints;

  const RandomFaceWidget({
    super.key,
    this.size = 200,
    this.faceColor = Colors.brown,
    this.strokeWidth = 2.0,
    this.filled = false,
    this.numPoints = 100,
  });

  @override
  State<RandomFaceWidget> createState() => _RandomFaceWidgetState();
}

class _RandomFaceWidgetState extends State<RandomFaceWidget> {
  late FaceContour _faceContour;

  @override
  void initState() {
    super.initState();
    _generateNewFace();
  }

  void _generateNewFace() {
    setState(() {
      _faceContour = FaceShapeGenerator.generateFaceContourPoints(numPoints: widget.numPoints);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        FaceWidget(
          faceContour: _faceContour,
          size: widget.size,
          faceColor: widget.faceColor,
          strokeWidth: widget.strokeWidth,
          filled: widget.filled,
        ),
        const SizedBox(height: 10),
        Text(
          'W: ${_faceContour.width.toStringAsFixed(1)}, H: ${_faceContour.height.toStringAsFixed(1)}',
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 10),
        ElevatedButton(
          onPressed: _generateNewFace,
          child: const Text('Generate New Face'),
        ),
      ],
    );
  }
}

/// Widget for displaying multiple face variations
class MultipleFacesWidget extends StatefulWidget {
  final int faceCount;
  final double faceSize;
  final double spacing;
  final Color faceColor;
  final double strokeWidth;
  final bool filled;
  final int numPoints;

  const MultipleFacesWidget({
    super.key,
    this.faceCount = 3,
    this.faceSize = 150,
    this.spacing = 15,
    this.faceColor = Colors.brown,
    this.strokeWidth = 2.0,
    this.filled = false,
    this.numPoints = 100,
  });

  @override
  State<MultipleFacesWidget> createState() => _MultipleFacesWidgetState();
}

class _MultipleFacesWidgetState extends State<MultipleFacesWidget> {
  late List<FaceContour> _faceContours;

  @override
  void initState() {
    super.initState();
    _generateAllFaces();
  }

  void _generateAllFaces() {
    setState(() {
      _faceContours = List.generate(
        widget.faceCount,
        (index) => FaceShapeGenerator.generateFaceContourPoints(numPoints: widget.numPoints),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Wrap(
          spacing: widget.spacing,
          runSpacing: widget.spacing,
          children: _faceContours.map((faceContour) {
            return FaceWidget(
              faceContour: faceContour,
              size: widget.faceSize,
              faceColor: widget.faceColor,
              strokeWidth: widget.strokeWidth,
              filled: widget.filled,
            );
          }).toList(),
        ),
        const SizedBox(height: 20),
        ElevatedButton(
          onPressed: _generateAllFaces,
          child: const Text('Generate All New'),
        ),
      ],
    );
  }
}

/// Widget for comparing filled vs outlined faces
class FaceComparisonWidget extends StatefulWidget {
  final double size;
  final double spacing;
  final Color faceColor;
  final double strokeWidth;
  final int numPoints;

  const FaceComparisonWidget({
    super.key,
    this.size = 150,
    this.spacing = 20,
    this.faceColor = Colors.brown,
    this.strokeWidth = 2.0,
    this.numPoints = 100,
  });

  @override
  State<FaceComparisonWidget> createState() => _FaceComparisonWidgetState();
}

class _FaceComparisonWidgetState extends State<FaceComparisonWidget> {
  late FaceContour _faceContour;

  @override
  void initState() {
    super.initState();
    _generateNewFace();
  }

  void _generateNewFace() {
    setState(() {
      _faceContour = FaceShapeGenerator.generateFaceContourPoints(numPoints: widget.numPoints);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Column(
              children: [
                const Text('Outlined', style: TextStyle(fontWeight: FontWeight.w500)),
                const SizedBox(height: 8),
                FaceWidget(
                  faceContour: _faceContour,
                  size: widget.size,
                  faceColor: widget.faceColor,
                  strokeWidth: widget.strokeWidth,
                  filled: false,
                ),
              ],
            ),
            SizedBox(width: widget.spacing),
            Column(
              children: [
                const Text('Filled', style: TextStyle(fontWeight: FontWeight.w500)),
                const SizedBox(height: 8),
                FaceWidget(
                  faceContour: _faceContour,
                  size: widget.size,
                  faceColor: widget.faceColor.withValues(alpha: 0.3),
                  strokeWidth: widget.strokeWidth,
                  filled: true,
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 20),
        ElevatedButton(
          onPressed: _generateNewFace,
          child: const Text('Generate New Comparison'),
        ),
      ],
    );
  }
}
