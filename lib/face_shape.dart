import 'dart:math';
import 'eye_shape.dart'; // For Point2D and RandomUtils

/// Utility class for generating egg-shaped points
class EggShapeUtils {
  /// Generate points for an egg shape using the formula: x²/a² * (1 + ky) + y²/b² = 1
  static List<Point2D> getEggShapePoints(double a, double b, double k, int segmentPoints) {
    final List<Point2D> result = [];
    
    // First quadrant: x positive, y positive
    for (int i = 0; i < segmentPoints; i++) {
      final double degree = (pi / 2 / segmentPoints) * i + 
          RandomUtils.randomFromInterval(-pi / 1.1 / segmentPoints, pi / 1.1 / segmentPoints);
      final double y = sin(degree) * b;
      final double x = sqrt(((1 - (y * y) / (b * b)) / (1 + k * y)) * a * a) + 
          RandomUtils.randomFromInterval(-a / 200.0, a / 200.0);
      result.add(Point2D(x, y));
    }
    
    // Second quadrant: x negative, y positive
    for (int i = segmentPoints; i > 0; i--) {
      final double degree = (pi / 2 / segmentPoints) * i + 
          RandomUtils.randomFromInterval(-pi / 1.1 / segmentPoints, pi / 1.1 / segmentPoints);
      final double y = sin(degree) * b;
      final double x = -sqrt(((1 - (y * y) / (b * b)) / (1 + k * y)) * a * a) + 
          RandomUtils.randomFromInterval(-a / 200.0, a / 200.0);
      result.add(Point2D(x, y));
    }
    
    // Third quadrant: x negative, y negative
    for (int i = 0; i < segmentPoints; i++) {
      final double degree = (pi / 2 / segmentPoints) * i + 
          RandomUtils.randomFromInterval(-pi / 1.1 / segmentPoints, pi / 1.1 / segmentPoints);
      final double y = -sin(degree) * b;
      final double x = -sqrt(((1 - (y * y) / (b * b)) / (1 + k * y)) * a * a) + 
          RandomUtils.randomFromInterval(-a / 200.0, a / 200.0);
      result.add(Point2D(x, y));
    }
    
    // Fourth quadrant: x positive, y negative
    for (int i = segmentPoints; i > 0; i--) {
      final double degree = (pi / 2 / segmentPoints) * i + 
          RandomUtils.randomFromInterval(-pi / 1.1 / segmentPoints, pi / 1.1 / segmentPoints);
      final double y = -sin(degree) * b;
      final double x = sqrt(((1 - (y * y) / (b * b)) / (1 + k * y)) * a * a) + 
          RandomUtils.randomFromInterval(-a / 200.0, a / 200.0);
      result.add(Point2D(x, y));
    }
    
    return result;
  }
}

/// Utility class for geometric calculations
class GeometryUtils {
  /// Find intersection points of a line with a rectangle
  static Point2D findIntersectionPoints(double radian, double a, double b) {
    double clampedRadian = radian.clamp(0.0, pi / 2);
    
    // Check if radian is close to 90 degrees
    if ((clampedRadian - pi / 2).abs() < 0.0001) {
      return Point2D(0, b);
    }
    
    // Slope of the line
    final double m = tan(clampedRadian);
    
    // Only checks the first quadrant
    final double y = m * a;
    if (y < b) {
      // It intersects with the left side
      return Point2D(a, y);
    } else {
      // It intersects with the top side
      final double x = b / m;
      return Point2D(x, b);
    }
  }
}

/// Generate rectangular face contour points
class RectangularFaceUtils {
  static List<Point2D> generateRectangularFaceContourPoints(double a, double b, int segmentPoints) {
    final List<Point2D> result = [];
    
    // First quadrant: x positive, y positive
    for (int i = 0; i < segmentPoints; i++) {
      final double degree = (pi / 2 / segmentPoints) * i + 
          RandomUtils.randomFromInterval(-pi / 11 / segmentPoints, pi / 11 / segmentPoints);
      final Point2D intersection = GeometryUtils.findIntersectionPoints(degree, a, b);
      result.add(Point2D(intersection.x, intersection.y));
    }
    
    // Second quadrant: x negative, y positive
    for (int i = segmentPoints; i > 0; i--) {
      final double degree = (pi / 2 / segmentPoints) * i + 
          RandomUtils.randomFromInterval(-pi / 11 / segmentPoints, pi / 11 / segmentPoints);
      final Point2D intersection = GeometryUtils.findIntersectionPoints(degree, a, b);
      result.add(Point2D(-intersection.x, intersection.y));
    }
    
    // Third quadrant: x negative, y negative
    for (int i = 0; i < segmentPoints; i++) {
      final double degree = (pi / 2 / segmentPoints) * i + 
          RandomUtils.randomFromInterval(-pi / 11 / segmentPoints, pi / 11 / segmentPoints);
      final Point2D intersection = GeometryUtils.findIntersectionPoints(degree, a, b);
      result.add(Point2D(-intersection.x, -intersection.y));
    }
    
    // Fourth quadrant: x positive, y negative
    for (int i = segmentPoints; i > 0; i--) {
      final double degree = (pi / 2 / segmentPoints) * i + 
          RandomUtils.randomFromInterval(-pi / 11 / segmentPoints, pi / 11 / segmentPoints);
      final Point2D intersection = GeometryUtils.findIntersectionPoints(degree, a, b);
      result.add(Point2D(intersection.x, -intersection.y));
    }
    
    return result;
  }
}

/// Contains the generated points and properties for a face contour
class FaceContour {
  final List<Point2D> points;
  final double width;
  final double height;
  final Point2D center;
  
  const FaceContour({
    required this.points,
    required this.width,
    required this.height,
    required this.center,
  });
}

/// Main class for generating face shapes
class FaceShapeGenerator {
  /// Generate face contour points with random variations
  static FaceContour generateFaceContourPoints({int numPoints = 100}) {
    // Generate random parameters for two face shapes
    final double faceSizeX0 = RandomUtils.randomFromInterval(50, 100);
    final double faceSizeY0 = RandomUtils.randomFromInterval(70, 100);
    final double faceSizeY1 = RandomUtils.randomFromInterval(50, 80);
    final double faceSizeX1 = RandomUtils.randomFromInterval(70, 100);
    
    final double faceK0 = RandomUtils.randomFromInterval(0.001, 0.005) * (Random().nextDouble() > 0.5 ? 1 : -1);
    final double faceK1 = RandomUtils.randomFromInterval(0.001, 0.005) * (Random().nextDouble() > 0.5 ? 1 : -1);
    
    final double face0TranslateX = RandomUtils.randomFromInterval(-5, 5);
    final double face0TranslateY = RandomUtils.randomFromInterval(-15, 15);
    final double face1TranslateY = RandomUtils.randomFromInterval(-5, 5);
    final double face1TranslateX = RandomUtils.randomFromInterval(-5, 25);
    
    // Randomly choose between egg and rectangular shapes
    final bool eggOrRect0 = Random().nextDouble() > 0.1;
    final bool eggOrRect1 = Random().nextDouble() > 0.3;
    
    // Generate two face shapes
    List<Point2D> results0 = eggOrRect0
        ? EggShapeUtils.getEggShapePoints(faceSizeX0, faceSizeY0, faceK0, numPoints)
        : RectangularFaceUtils.generateRectangularFaceContourPoints(faceSizeX0, faceSizeY0, numPoints);
        
    List<Point2D> results1 = eggOrRect1
        ? EggShapeUtils.getEggShapePoints(faceSizeX1, faceSizeY1, faceK1, numPoints)
        : RectangularFaceUtils.generateRectangularFaceContourPoints(faceSizeX1, faceSizeY1, numPoints);
    
    // Apply translations
    for (int i = 0; i < results0.length; i++) {
      results0[i] = Point2D(results0[i].x + face0TranslateX, results0[i].y + face0TranslateY);
      results1[i] = Point2D(results1[i].x + face1TranslateX, results1[i].y + face1TranslateY);
    }
    
    // Combine the two shapes with weighted blending
    final List<Point2D> results = [];
    Point2D center = const Point2D(0, 0);
    
    for (int i = 0; i < results0.length; i++) {
      final int offsetIndex = (i + (results0.length ~/ 4)) % results0.length;
      final Point2D blendedPoint = Point2D(
        results0[i].x * 0.7 + results1[offsetIndex].y * 0.3,
        results0[i].y * 0.7 - results1[offsetIndex].x * 0.3,
      );
      results.add(blendedPoint);
      center = Point2D(center.x + blendedPoint.x, center.y + blendedPoint.y);
    }
    
    // Calculate center
    center = Point2D(center.x / results.length, center.y / results.length);
    
    // Center the face
    final List<Point2D> centeredResults = [];
    for (final Point2D point in results) {
      centeredResults.add(Point2D(point.x - center.x, point.y - center.y));
    }
    
    // Calculate width and height
    final double width = centeredResults[0].x - centeredResults[centeredResults.length ~/ 2].x;
    final double height = centeredResults[centeredResults.length ~/ 4].y - 
                         centeredResults[(centeredResults.length * 3) ~/ 4].y;
    
    // Add the first point to the end to close the shape
    centeredResults.add(centeredResults[0]);
    centeredResults.add(centeredResults[1]);
    
    return FaceContour(
      points: centeredResults,
      width: width.abs(),
      height: height.abs(),
      center: const Point2D(0, 0),
    );
  }
}
